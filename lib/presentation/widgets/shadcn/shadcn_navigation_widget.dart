import 'package:flutter/material.dart' hide Theme, ThemeData;
import 'package:shadcn_flutter/shadcn_flutter.dart';
import '../../../core/theme/app_theme.dart';

/// A shadcn-based navigation widget that replaces the custom bottom navigation
/// while maintaining the same API and visual consistency
class ShadcnNavigationWidget extends StatelessWidget {
  final int currentIndex;
  final List<ShadcnNavigationItem> items;
  final Function(int) onTap;
  final Widget? floatingActionButton;
  final VoidCallback? onCreateTap;

  const ShadcnNavigationWidget({
    super.key,
    required this.currentIndex,
    required this.items,
    required this.onTap,
    this.floatingActionButton,
    this.onCreateTap,
  });

  @override
  Widget build(BuildContext context) {
    final materialTheme = Material.Theme.of(context);

    return Container(
      height: 80,
      decoration: BoxDecoration(
        color: materialTheme.colorScheme.surface,
        border: Border(
          top: BorderSide(
            color: materialTheme.colorScheme.outline.withValues(alpha: 0.1),
            width: 0.5,
          ),
        ),
      ),
      child: Row(
        children: [
          // Navigation items
          ...items.asMap().entries.map((entry) {
            final index = entry.key;
            final item = entry.value;
            final isSelected = index == currentIndex;
            
            return Expanded(
              child: _buildNavItem(
                theme: materialTheme,
                item: item,
                isSelected: isSelected,
                onTap: () => onTap(index),
              ),
            );
          }),
          
          // Create button if provided
          if (onCreateTap != null)
            Expanded(
              child: _buildCreateButton(materialTheme),
            ),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required Material.ThemeData theme,
    required ShadcnNavigationItem item,
    required bool isSelected,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      behavior: HitTestBehavior.opaque,
      child: Container(
        height: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Icon with smooth transition
            AnimatedSwitcher(
              duration: const Duration(milliseconds: 200),
              child: Icon(
                isSelected ? item.selectedIcon : item.icon,
                key: ValueKey(isSelected),
                color: isSelected
                    ? (item.activeColor ?? theme.colorScheme.primary)
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                size: 20,
              ),
            ),
            const SizedBox(height: 4),
            
            // Label
            Text(
              item.label,
              style: theme.textTheme.labelSmall?.copyWith(
                color: isSelected
                    ? (item.activeColor ?? theme.colorScheme.primary)
                    : theme.colorScheme.onSurface.withValues(alpha: 0.6),
                fontWeight: isSelected ? FontWeight.w500 : FontWeight.w400,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCreateButton(Material.ThemeData theme) {
    return GestureDetector(
      onTap: onCreateTap,
      child: Container(
        height: double.infinity,
        padding: const EdgeInsets.symmetric(vertical: 12),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 28,
              height: 28,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8),
                color: theme.colorScheme.onSurface.withValues(alpha: 0.1),
                border: Border.all(
                  color: theme.colorScheme.outline.withValues(alpha: 0.2),
                  width: 0.5,
                ),
              ),
              child: Icon(
                Icons.add,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.8),
                size: 18,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Create',
              style: theme.textTheme.labelSmall?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                fontWeight: FontWeight.w400,
                fontSize: 10,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Navigation item data class
class ShadcnNavigationItem {
  final IconData icon;
  final IconData selectedIcon;
  final String label;
  final Color? activeColor;
  final List<Color>? gradientColors;

  const ShadcnNavigationItem({
    required this.icon,
    required this.selectedIcon,
    required this.label,
    this.activeColor,
    this.gradientColors,
  });
}

/// A tab view widget that works with shadcn navigation
class ShadcnTabView extends StatelessWidget {
  final int currentIndex;
  final List<Widget> children;
  final Duration animationDuration;

  const ShadcnTabView({
    super.key,
    required this.currentIndex,
    required this.children,
    this.animationDuration = const Duration(milliseconds: 300),
  });

  @override
  Widget build(BuildContext context) {
    return IndexedStack(
      index: currentIndex,
      children: children,
    );
  }
}

/// A complete navigation scaffold using shadcn components
class ShadcnNavigationScaffold extends StatelessWidget {
  final int currentIndex;
  final List<ShadcnNavigationItem> navigationItems;
  final List<Widget> pages;
  final Function(int) onNavigationTap;
  final VoidCallback? onCreateTap;
  final Widget? floatingActionButton;
  final PreferredSizeWidget? appBar;
  final Widget? drawer;
  final Widget? endDrawer;
  final Color? backgroundColor;

  const ShadcnNavigationScaffold({
    super.key,
    required this.currentIndex,
    required this.navigationItems,
    required this.pages,
    required this.onNavigationTap,
    this.onCreateTap,
    this.floatingActionButton,
    this.appBar,
    this.drawer,
    this.endDrawer,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final materialTheme = Material.Theme.of(context);

    return Material.Scaffold(
      appBar: appBar,
      drawer: drawer,
      endDrawer: endDrawer,
      extendBody: true,
      backgroundColor: backgroundColor ?? materialTheme.colorScheme.surface,
      body: AnimatedContainer(
        duration: const Duration(milliseconds: 500),
        curve: Curves.easeInOut,
        decoration: BoxDecoration(
          gradient: materialTheme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    materialTheme.colorScheme.surface,
                    materialTheme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: ShadcnTabView(
          currentIndex: currentIndex,
          children: pages,
        ),
      ),
      bottomNavigationBar: ShadcnNavigationWidget(
        currentIndex: currentIndex,
        items: navigationItems,
        onTap: onNavigationTap,
        onCreateTap: onCreateTap,
        floatingActionButton: floatingActionButton,
      ),
      floatingActionButton: floatingActionButton,
    );
  }
}
