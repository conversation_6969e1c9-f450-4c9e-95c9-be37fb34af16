import 'package:flutter/material.dart' hide Theme, Card, Colors;
import 'package:shadcn_flutter/shadcn_flutter.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/theme/shadcn_theme.dart';

/// A shadcn-based card widget that replaces our custom GradientCard
/// while maintaining the same API and visual consistency
class ShadcnCardWidget extends StatelessWidget {
  final Widget child;
  final List<Color>? gradient;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? height;
  final double? width;
  final VoidCallback? onTap;
  final BorderRadius? borderRadius;
  final bool glassEffect;
  final double elevation;
  final int animationDelay;
  final String? title;
  final String? description;
  final Widget? header;
  final Widget? footer;

  const ShadcnCardWidget({
    super.key,
    required this.child,
    this.gradient,
    this.padding,
    this.margin,
    this.height,
    this.width,
    this.onTap,
    this.borderRadius,
    this.glassEffect = false,
    this.elevation = 0,
    this.animationDelay = 0,
    this.title,
    this.description,
    this.header,
    this.footer,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    Widget cardContent = _buildCardContent(theme);
    
    // Apply gradient if specified
    if (gradient != null) {
      cardContent = Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: gradient!,
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: borderRadius ?? BorderRadius.circular(24),
          boxShadow: elevation > 0 ? [
            BoxShadow(
              color: gradient!.first.withValues(alpha: 0.3),
              blurRadius: elevation,
              offset: Offset(0, elevation / 2),
            ),
          ] : null,
        ),
        child: Card(
          elevation: 0,
          color: const Color(0x00000000),
          shape: RoundedRectangleBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(24),
          ),
          child: cardContent,
        ),
      );
    } else {
      // Use standard shadcn Card
      cardContent = Card(
        elevation: elevation,
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(24),
        ),
        child: cardContent,
      );
    }

    // Apply size constraints
    if (width != null || height != null) {
      cardContent = SizedBox(
        width: width,
        height: height,
        child: cardContent,
      );
    }

    // Apply margin
    if (margin != null) {
      cardContent = Padding(
        padding: margin!,
        child: cardContent,
      );
    }

    // Apply tap handler
    if (onTap != null) {
      cardContent = InkWell(
        onTap: onTap,
        borderRadius: borderRadius ?? BorderRadius.circular(24),
        child: cardContent,
      );
    }

    return cardContent;
  }

  Widget _buildCardContent(ThemeData theme) {
    final List<Widget> children = [];

    // Add header if provided
    if (header != null) {
      children.add(header!);
    } else if (title != null || description != null) {
      children.add(_buildDefaultHeader(theme));
    }

    // Add main content
    children.add(
      Expanded(
        child: Padding(
          padding: padding ?? const EdgeInsets.all(16),
          child: child,
        ),
      ),
    );

    // Add footer if provided
    if (footer != null) {
      children.add(footer!);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: children,
    );
  }

  Widget _buildDefaultHeader(ThemeData theme) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null)
            Text(
              title!,
              style: theme.textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),
          if (description != null) ...[
            const SizedBox(height: 4),
            Text(
              description!,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
            ),
          ],
        ],
      ),
    );
  }
}

/// A specialized card for displaying flashcards with shadcn design
class ShadcnFlashCardWidget extends StatelessWidget {
  final String content;
  final String label;
  final List<Color>? gradient;
  final VoidCallback? onTap;
  final bool isFlipped;
  final Widget? badge;
  final List<Widget>? actions;

  const ShadcnFlashCardWidget({
    super.key,
    required this.content,
    required this.label,
    this.gradient,
    this.onTap,
    this.isFlipped = false,
    this.badge,
    this.actions,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ShadcnCardWidget(
      gradient: gradient,
      onTap: onTap,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Header with label and badge
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: theme.colorScheme.primaryContainer.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: theme.colorScheme.primary.withValues(alpha: 0.3),
                  ),
                ),
                child: Text(
                  label,
                  style: theme.textTheme.labelSmall?.copyWith(
                    color: theme.colorScheme.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              if (badge != null) badge!,
            ],
          ),
          
          const SizedBox(height: 24),
          
          // Content
          Expanded(
            child: Center(
              child: SingleChildScrollView(
                child: Text(
                  content,
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w500,
                    height: 1.4,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
            ),
          ),
          
          // Actions
          if (actions != null && actions!.isNotEmpty) ...[
            const SizedBox(height: 24),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: actions!,
            ),
          ],
        ],
      ),
    );
  }
}

/// A simple card wrapper that provides consistent styling
class ShadcnSimpleCard extends StatelessWidget {
  final Widget child;
  final EdgeInsetsGeometry? padding;
  final VoidCallback? onTap;

  const ShadcnSimpleCard({
    super.key,
    required this.child,
    this.padding,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    Widget content = Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(24),
      ),
      child: Padding(
        padding: padding ?? const EdgeInsets.all(16),
        child: child,
      ),
    );

    if (onTap != null) {
      content = InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(24),
        child: content,
      );
    }

    return content;
  }
}
