import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import '../viewmodels/deck_detail_view_model.dart';
import '../../core/di/injection_container.dart';
import '../../domain/entities/deck.dart';
import '../../domain/entities/flashcard.dart';
import '../../core/theme/app_theme.dart';
import '../widgets/shadcn/index.dart';

/// Migrated Deck Detail View using shadcn_flutter components
/// Maintains the same functionality while using shadcn design system
class DeckDetailViewShadcn extends StatefulWidget {
  final Deck deck;

  const DeckDetailViewShadcn({super.key, required this.deck});

  @override
  State<DeckDetailViewShadcn> createState() => _DeckDetailViewShadcnState();
}

class _DeckDetailViewShadcnState extends State<DeckDetailViewShadcn> {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => getIt<DeckDetailViewModel>()..loadDeck(widget.deck.id),
      child: Consumer<DeckDetailViewModel>(
        builder: (context, viewModel, child) {
          return Scaffold(
            backgroundColor: Colors.transparent,
            body: _buildBody(context, viewModel),
            floatingActionButton: _buildFloatingActionButton(context, viewModel),
          );
        },
      ),
    );
  }

  Widget _buildBody(BuildContext context, DeckDetailViewModel viewModel) {
    final theme = Theme.of(context);
    
    if (viewModel.isLoading) {
      return _buildLoadingState(theme);
    }
    
    if (viewModel.hasError) {
      return _buildErrorState(theme, viewModel);
    }
    
    final deck = viewModel.currentDeck ?? widget.deck;
    
    return Container(
      decoration: BoxDecoration(
        gradient: theme.brightness == Brightness.dark
            ? AppTheme.surfaceLinearGradient
            : LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.surface,
                  theme.colorScheme.surfaceContainerHighest,
                ],
              ),
      ),
      child: CustomScrollView(
        physics: const BouncingScrollPhysics(),
        slivers: [
          // App Bar
          SliverAppBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            leading: Padding(
              padding: const EdgeInsets.all(8),
              child: Button(
                onPressed: () => viewModel.navigateBack(),
                variant: ButtonVariant.ghost,
                size: ButtonSize.sm,
                child: Icon(
                  PhosphorIcons.arrowLeft(),
                  size: 20,
                ),
              ),
            ),
            title: Text(
              deck.name,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: theme.colorScheme.onSurface,
              ),
            ),
            actions: [
              if (viewModel.canStartStudy)
                Padding(
                  padding: const EdgeInsets.only(right: 16),
                  child: Button.ghost(
                    onPressed: () => viewModel.navigateToStudy(),
                    child: Icon(
                      PhosphorIcons.graduationCap(),
                      size: 20,
                    ),
                  ),
                ),
            ],
            floating: true,
            snap: true,
          ),

          const SliverToBoxAdapter(child: SizedBox(height: 20)),

          // Hero Section
          SliverToBoxAdapter(
            child: _buildHeroSection(theme, deck),
          ),

          // Stats Section
          SliverToBoxAdapter(
            child: _buildStatsSection(theme, viewModel),
          ),

          // Flashcards Section
          if (viewModel.hasCards)
            SliverToBoxAdapter(
              child: _buildFlashcardsHeader(theme, viewModel),
            ),

          // Flashcards List
          if (viewModel.hasCards)
            SliverPadding(
              padding: const EdgeInsets.fromLTRB(24, 0, 24, 120),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, index) {
                    final flashcard = viewModel.flashcards[index];
                    return Padding(
                      padding: const EdgeInsets.only(bottom: 16),
                      child: _FlashcardCardShadcn(
                        flashcard: flashcard,
                        index: index,
                        onEdit: () => viewModel.handleEditFlashcard(flashcard),
                        onDelete: () => viewModel.handleDeleteFlashcard(flashcard),
                      ),
                    );
                  },
                  childCount: viewModel.flashcards.length,
                ),
              ),
            )
          else
            SliverToBoxAdapter(
              child: _buildEmptyState(context, viewModel),
            ),
        ],
      ),
    );
  }

  Widget _buildLoadingState(ThemeData theme) {
    return Container(
      decoration: BoxDecoration(
        gradient: theme.brightness == Brightness.dark
            ? AppTheme.surfaceLinearGradient
            : LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.surface,
                  theme.colorScheme.surfaceContainerHighest,
                ],
              ),
      ),
      child: Center(
        child: ShadcnCardWidget(
          padding: const EdgeInsets.all(40),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: AppTheme.primaryGradient),
                  borderRadius: BorderRadius.circular(30),
                ),
                child: const Center(
                  child: CircularProgressIndicator(
                    color: Colors.white,
                    strokeWidth: 3,
                  ),
                ),
              ),
              const SizedBox(height: 24),
              Text(
                'Loading Deck',
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Please wait...',
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(ThemeData theme, DeckDetailViewModel viewModel) {
    return Container(
      decoration: BoxDecoration(
        gradient: theme.brightness == Brightness.dark
            ? AppTheme.surfaceLinearGradient
            : LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  theme.colorScheme.surface,
                  theme.colorScheme.surfaceContainerHighest,
                ],
              ),
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Row(
                children: [
                  Button(
                    onPressed: () => Navigator.of(context).pop(),
                    variant: ButtonVariant.ghost,
                    size: ButtonSize.sm,
                    child: Icon(PhosphorIcons.arrowLeft()),
                  ),
                ],
              ),
              Expanded(
                child: Center(
                  child: ShadcnCardWidget(
                    padding: const EdgeInsets.all(40),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 80,
                          height: 80,
                          decoration: BoxDecoration(
                            color: theme.colorScheme.error.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(40),
                          ),
                          child: Icon(
                            PhosphorIcons.warning(),
                            size: 40,
                            color: theme.colorScheme.error,
                          ),
                        ),
                        const SizedBox(height: 24),
                        Text(
                          'Error Loading Deck',
                          style: theme.textTheme.headlineSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.colorScheme.onSurface,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          viewModel.error ?? 'An unknown error occurred',
                          textAlign: TextAlign.center,
                          style: theme.textTheme.bodyLarge?.copyWith(
                            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                          ),
                        ),
                        const SizedBox(height: 32),
                        ShadcnButtonWidget(
                          text: 'Retry',
                          variant: ShadcnButtonVariant.primary,
                          onPressed: () => viewModel.loadDeck(widget.deck.id),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeroSection(ThemeData theme, Deck deck) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      child: ShadcnCardWidget(
        padding: const EdgeInsets.all(32),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: 64,
              height: 64,
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: AppTheme.primaryGradient),
                borderRadius: BorderRadius.circular(32),
              ),
              child: Icon(
                PhosphorIcons.stack(),
                color: Colors.white,
                size: 32,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              deck.name,
              style: theme.textTheme.headlineLarge?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (deck.description.isNotEmpty) ...[
              const SizedBox(height: 12),
              Text(
                deck.description,
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  fontWeight: FontWeight.w400,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatsSection(ThemeData theme, DeckDetailViewModel viewModel) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      child: Row(
        children: [
          Expanded(
            child: _buildStatCard(
              theme: theme,
              title: 'Cards',
              value: '${viewModel.totalCards}',
              gradient: AppTheme.primaryGradient,
              icon: PhosphorIcons.cards(),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: _buildStatCard(
              theme: theme,
              title: 'Reviews',
              value: '${viewModel.totalReviews}',
              gradient: AppTheme.secondaryGradient,
              icon: PhosphorIcons.clockCounterClockwise(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required ThemeData theme,
    required String title,
    required String value,
    required List<Color> gradient,
    required IconData icon,
  }) {
    return ShadcnCardWidget(
      padding: const EdgeInsets.all(24),
      gradient: gradient.map((c) => c.withValues(alpha: 0.1)).toList(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: gradient.first.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              color: gradient.first,
              size: 20,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            value,
            style: theme.textTheme.headlineMedium?.copyWith(
              color: theme.colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: theme.textTheme.bodyMedium?.copyWith(
              color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFlashcardsHeader(ThemeData theme, DeckDetailViewModel viewModel) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Icon(
                PhosphorIcons.cards(),
                color: theme.colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Flashcards',
                style: theme.textTheme.titleLarge?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          Row(
            children: [
              Badge(
                child: Text(
                  '${viewModel.totalCards}',
                  style: theme.textTheme.labelMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Button.ghost(
                onPressed: () => viewModel.navigateToAddFlashcard(),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(PhosphorIcons.plus(), size: 16),
                    const SizedBox(width: 4),
                    const Text('Add'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context, DeckDetailViewModel viewModel) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(48),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: AppTheme.primaryGradient.map((c) => c.withValues(alpha: 0.3)).toList(),
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(60),
              ),
              child: Icon(
                PhosphorIcons.cards(),
                size: 60,
                color: AppTheme.primaryGradient.first,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Flashcards Yet',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Add your first flashcard to start studying!',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ShadcnButtonWidget(
              text: 'Add Flashcard',
              icon: PhosphorIcons.plus(),
              variant: ShadcnButtonVariant.primary,
              onPressed: () => viewModel.navigateToAddFlashcard(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton(BuildContext context, DeckDetailViewModel viewModel) {
    return ShadcnFloatingActionButton(
      onPressed: () => viewModel.navigateToAddFlashcard(),
      icon: PhosphorIcons.plus(),
    );
  }
}

/// Shadcn-based Flashcard Card Widget
class _FlashcardCardShadcn extends StatelessWidget {
  final Flashcard flashcard;
  final int index;
  final VoidCallback onEdit;
  final VoidCallback onDelete;

  const _FlashcardCardShadcn({
    required this.flashcard,
    required this.index,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final gradients = [
      AppTheme.primaryGradient,
      AppTheme.secondaryGradient,
      AppTheme.accentGradient,
      [const Color(0xFFEC4899), const Color(0xFFDC2626)],
    ];
    final gradient = gradients[index % gradients.length];

    return ShadcnCardWidget(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 32,
                height: 32,
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: gradient),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Icon(
                  PhosphorIcons.cards(),
                  color: Colors.white,
                  size: 16,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Card ${index + 1}',
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              PopupMenuButton<String>(
                onSelected: (value) {
                  if (value == 'edit') {
                    onEdit();
                  } else if (value == 'delete') {
                    onDelete();
                  }
                },
                itemBuilder: (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        Icon(PhosphorIcons.pencil(), size: 16),
                        const SizedBox(width: 8),
                        const Text('Edit'),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'delete',
                    child: Row(
                      children: [
                        Icon(PhosphorIcons.trash(), size: 16, color: theme.colorScheme.error),
                        const SizedBox(width: 8),
                        Text('Delete', style: TextStyle(color: theme.colorScheme.error)),
                      ],
                    ),
                  ),
                ],
                child: Icon(
                  PhosphorIcons.dotsThreeVertical(),
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.5),
                  size: 16,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: gradient.first.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: gradient.first.withValues(alpha: 0.1),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      PhosphorIcons.question(),
                      size: 14,
                      color: gradient.first,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Question',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: gradient.first,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  flashcard.front,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: gradient.last.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: gradient.last.withValues(alpha: 0.1),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      PhosphorIcons.lightbulb(),
                      size: 14,
                      color: gradient.last,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      'Answer',
                      style: theme.textTheme.labelSmall?.copyWith(
                        color: gradient.last,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  flashcard.back,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
