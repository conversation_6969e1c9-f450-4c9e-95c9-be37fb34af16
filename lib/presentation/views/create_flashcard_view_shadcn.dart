import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import '../viewmodels/create_flashcard_view_model.dart';
import '../../core/theme/app_theme.dart';
import '../../core/di/injection_container.dart';
import '../widgets/shadcn/index.dart';

/// Migrated Create Flashcard View using shadcn_flutter components
/// Maintains the same functionality while using shadcn design system
class CreateFlashcardViewShadcn extends StatefulWidget {
  final String deckId;

  const CreateFlashcardViewShadcn({
    super.key,
    required this.deckId,
  });

  @override
  State<CreateFlashcardViewShadcn> createState() => _CreateFlashcardViewShadcnState();
}

class _CreateFlashcardViewShadcnState extends State<CreateFlashcardViewShadcn> {
  final _formKey = GlobalKey<FormState>();
  final _frontController = TextEditingController();
  final _backController = TextEditingController();

  @override
  void dispose() {
    _frontController.dispose();
    _backController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return ChangeNotifierProvider(
      create: (_) => CreateFlashcardViewModel(
        addFlashcardToDeck: getIt(),
        navigationService: getIt(),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          decoration: BoxDecoration(
            gradient: theme.brightness == Brightness.dark
                ? AppTheme.surfaceLinearGradient
                : LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      theme.colorScheme.surface,
                      theme.colorScheme.surfaceContainerHighest,
                    ],
                  ),
          ),
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // App Bar
              SliverAppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Button.ghost(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Icon(
                      PhosphorIcons.arrowLeft(),
                      size: 20,
                    ),
                  ),
                ),
                title: Text(
                  'Create Flashcard',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                floating: true,
                snap: true,
              ),

              const SliverToBoxAdapter(child: SizedBox(height: 20)),

              // Hero Section
              SliverToBoxAdapter(
                child: _buildHeroSection(theme),
              ),

              // Form Section
              SliverToBoxAdapter(
                child: _buildFormSection(theme),
              ),

              // Create Button
              SliverToBoxAdapter(
                child: _buildCreateButton(theme),
              ),

              const SliverToBoxAdapter(child: SizedBox(height: 100)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 40),
      child: ShadcnCardWidget(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: AppTheme.secondaryGradient),
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                PhosphorIcons.cards(),
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Create Flashcard',
              style: theme.textTheme.headlineMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Add both the question and answer to create your flashcard',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      child: Consumer<CreateFlashcardViewModel>(
        builder: (context, viewModel, _) {
          return Form(
            key: _formKey,
            child: Column(
              children: [
                // Front side (Question)
                ShadcnCardWidget(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppTheme.primaryGradient.first.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              PhosphorIcons.question(),
                              color: AppTheme.primaryGradient.first,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Question (Front)',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      ShadcnTextAreaWidget(
                        controller: _frontController,
                        label: 'Question',
                        hint: 'Enter the question or term...',
                        maxLines: 4,
                        onChanged: viewModel.updateFront,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter a question';
                          }
                          return null;
                        },
                        required: true,
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 24),
                
                // Back side (Answer)
                ShadcnCardWidget(
                  padding: const EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: AppTheme.secondaryGradient.first.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(20),
                            ),
                            child: Icon(
                              PhosphorIcons.lightbulb(),
                              color: AppTheme.secondaryGradient.first,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Text(
                            'Answer (Back)',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: theme.colorScheme.onSurface,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 20),
                      ShadcnTextAreaWidget(
                        controller: _backController,
                        label: 'Answer',
                        hint: 'Enter the answer or definition...',
                        maxLines: 4,
                        onChanged: viewModel.updateBack,
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return 'Please enter an answer';
                          }
                          return null;
                        },
                        required: true,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCreateButton(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Consumer<CreateFlashcardViewModel>(
        builder: (context, viewModel, _) {
          return ShadcnButtonWidget(
            text: viewModel.isLoading ? 'Creating Flashcard...' : 'Create Flashcard',
            icon: viewModel.isLoading ? null : PhosphorIcons.plus(),
            variant: ShadcnButtonVariant.primary,
            isLoading: viewModel.isLoading,
            isExpanded: true,
            height: 56,
            onPressed: viewModel.isLoading 
                ? null 
                : () async {
                    if (_formKey.currentState!.validate()) {
                      await viewModel.createFlashcard(widget.deckId);
                      if (context.mounted && !viewModel.hasError) {
                        Navigator.of(context).pop(true);
                      }
                    }
                  },
          );
        },
      ),
    );
  }
}
