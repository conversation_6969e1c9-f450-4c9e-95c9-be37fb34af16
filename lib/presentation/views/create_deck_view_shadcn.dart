import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:phosphor_flutter/phosphor_flutter.dart';
import '../viewmodels/create_deck_view_model.dart';
import '../../core/theme/app_theme.dart';
import '../../core/di/injection_container.dart';
import '../../domain/usecases/deck/create_deck.dart';
import '../../core/services/navigation_service.dart';
import '../widgets/shadcn/index.dart';

/// Migrated Create Deck View using shadcn_flutter components
/// Maintains the same functionality while using shadcn design system
class CreateDeckViewShadcn extends StatefulWidget {
  const CreateDeckViewShadcn({super.key});

  @override
  State<CreateDeckViewShadcn> createState() => _CreateDeckViewShadcnState();
}

class _CreateDeckViewShadcnState extends State<CreateDeckViewShadcn> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return ChangeNotifierProvider(
      create: (_) => CreateDeckViewModel(
        createDeck: getIt<CreateDeck>(),
        navigationService: getIt<NavigationService>(),
      ),
      child: Scaffold(
        backgroundColor: Colors.transparent,
        body: Container(
          decoration: BoxDecoration(
            gradient: theme.brightness == Brightness.dark
                ? AppTheme.surfaceLinearGradient
                : LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      theme.colorScheme.surface,
                      theme.colorScheme.surfaceContainerHighest,
                    ],
                  ),
          ),
          child: CustomScrollView(
            physics: const BouncingScrollPhysics(),
            slivers: [
              // App Bar
              SliverAppBar(
                backgroundColor: Colors.transparent,
                elevation: 0,
                leading: Padding(
                  padding: const EdgeInsets.all(8),
                  child: Button.ghost(
                    onPressed: () => Navigator.of(context).pop(),
                    child: Icon(
                      PhosphorIcons.arrowLeft(),
                      size: 20,
                    ),
                  ),
                ),
                title: Text(
                  'Create Deck',
                  style: theme.textTheme.headlineSmall?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                floating: true,
                snap: true,
              ),

              const SliverToBoxAdapter(child: SizedBox(height: 20)),

              // Hero Section
              SliverToBoxAdapter(
                child: _buildHeroSection(theme),
              ),

              // Form Section
              SliverToBoxAdapter(
                child: _buildFormSection(theme),
              ),

              // Create Button
              SliverToBoxAdapter(
                child: _buildCreateButton(theme),
              ),

              const SliverToBoxAdapter(child: SizedBox(height: 100)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeroSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 40),
      child: ShadcnCardWidget(
        padding: const EdgeInsets.all(40),
        child: Column(
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                gradient: LinearGradient(colors: AppTheme.primaryGradient),
                borderRadius: BorderRadius.circular(40),
              ),
              child: Icon(
                PhosphorIcons.stack(),
                color: Colors.white,
                size: 40,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'Create New Deck',
              style: theme.textTheme.headlineMedium?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Build your personalized learning experience',
              style: theme.textTheme.bodyMedium?.copyWith(
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFormSection(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 32),
      child: Consumer<CreateDeckViewModel>(
        builder: (context, viewModel, _) {
          return Form(
            key: _formKey,
            child: Column(
              children: [
                ShadcnInputWidget(
                  controller: _nameController,
                  label: 'Deck Name',
                  hint: 'Enter a name for your deck',
                  prefixIcon: PhosphorIcons.textT(),
                  onChanged: viewModel.updateName,
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter a deck name';
                    }
                    return null;
                  },
                  required: true,
                ),
                const SizedBox(height: 24),
                ShadcnTextAreaWidget(
                  controller: _descriptionController,
                  label: 'Description',
                  hint: 'Describe your deck (optional)',
                  maxLines: 3,
                  onChanged: viewModel.updateDescription,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildCreateButton(ThemeData theme) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      child: Consumer<CreateDeckViewModel>(
        builder: (context, viewModel, _) {
          return ShadcnButtonWidget(
            text: viewModel.isLoading ? 'Creating Deck...' : 'Create Deck',
            icon: viewModel.isLoading ? null : PhosphorIcons.plus(),
            variant: ShadcnButtonVariant.primary,
            isLoading: viewModel.isLoading,
            isExpanded: true,
            height: 56,
            onPressed: viewModel.isLoading 
                ? null 
                : () async {
                    if (_formKey.currentState!.validate()) {
                      await viewModel.createDeck();
                      if (context.mounted && !viewModel.hasError) {
                        Navigator.of(context).pop(true);
                      }
                    }
                  },
          );
        },
      ),
    );
  }
}
