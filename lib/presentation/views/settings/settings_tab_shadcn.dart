import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../viewmodels/user_view_model.dart';
import '../../viewmodels/theme_view_model.dart';
import '../../widgets/shadcn/index.dart';
import '../../../core/theme/app_theme.dart';

/// Migrated Settings Tab using shadcn_flutter components
/// Maintains the same functionality and MVVM architecture while using shadcn design system
class SettingsTabShadcn extends StatefulWidget {
  const SettingsTabShadcn({super.key});

  @override
  State<SettingsTabShadcn> createState() => _SettingsTabShadcnState();
}

class _SettingsTabShadcnState extends State<SettingsTabShadcn> {

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final user = context.watch<UserViewModel>().currentUser;
    
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: Container(
        decoration: BoxDecoration(
          gradient: theme.brightness == Brightness.dark
              ? AppTheme.surfaceLinearGradient
              : LinearGradient(
                  begin: Alignment.topCenter,
                  end: Alignment.bottomCenter,
                  colors: [
                    theme.colorScheme.surface,
                    theme.colorScheme.surfaceContainerHighest,
                  ],
                ),
        ),
        child: CustomScrollView(
          physics: const BouncingScrollPhysics(),
          slivers: [
            const SliverToBoxAdapter(child: SizedBox(height: 60)),

            // User Profile Section
            if (user != null)
              SliverToBoxAdapter(
                child: _buildUserProfile(context, user),
              ),

            // Appearance Section
            SliverToBoxAdapter(
              child: _buildAppearanceSection(context),
            ),

            // Study Preferences
            SliverToBoxAdapter(
              child: _buildStudyPreferencesSection(context),
            ),

            // Account Actions
            SliverToBoxAdapter(
              child: _buildAccountSection(context),
            ),

            const SliverToBoxAdapter(child: SizedBox(height: 120)),
          ],
        ),
      ),
    );
  }

  Widget _buildUserProfile(BuildContext context, dynamic user) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: ShadcnCardWidget(
        padding: const EdgeInsets.all(32),
        child: Row(
          children: [
            Avatar(
              initials: user.name.substring(0, 1).toUpperCase(),
              size: 64,
            ),
            const SizedBox(width: 20),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: theme.textTheme.titleLarge?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Text(
                    user.email,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ],
              ),
            ),
            Button.ghost(
              onPressed: () {
                // Edit profile
              },
              child: const Icon(Icons.edit_outlined, size: 16),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAppearanceSection(BuildContext context) {
    final theme = Theme.of(context);
    final themeViewModel = context.watch<ThemeViewModel>();

    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: ShadcnCardWidget(
        gradient: AppTheme.secondaryGradient.map((c) => c.withValues(alpha: 0.08)).toList(),
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.brightness_6_outlined,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  size: 24,
                ),
                const SizedBox(width: 16),
                Text(
                  'Appearance',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildThemeOption(
              theme: theme,
              title: 'Theme Mode',
              subtitle: _getThemeModeText(themeViewModel.themeMode),
              onTap: () => _showThemeSelector(context, themeViewModel),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStudyPreferencesSection(BuildContext context) {
    final theme = Theme.of(context);
    final userViewModel = context.watch<UserViewModel>();

    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: ShadcnCardWidget(
        gradient: AppTheme.accentGradient.map((c) => c.withValues(alpha: 0.08)).toList(),
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.school_outlined,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  size: 24,
                ),
                const SizedBox(width: 16),
                Text(
                  'Study Preferences',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildPreferenceToggle(
              theme: theme,
              icon: Icons.notifications_outlined,
              title: 'Study Reminders',
              subtitle: 'Get notified to maintain your streak',
              value: userViewModel.getPreference('studyReminders', defaultValue: true),
              onChanged: (value) {
                userViewModel.updatePreferences({'studyReminders': value});
              },
            ),
            const SizedBox(height: 20),
            _buildPreferenceToggle(
              theme: theme,
              icon: Icons.volume_up_outlined,
              title: 'Sound Effects',
              subtitle: 'Play sounds during study sessions',
              value: userViewModel.getPreference('soundEffects', defaultValue: true),
              onChanged: (value) {
                userViewModel.updatePreferences({'soundEffects': value});
              },
            ),
            const SizedBox(height: 20),
            _buildPreferenceToggle(
              theme: theme,
              icon: Icons.vibration,
              title: 'Haptic Feedback',
              subtitle: 'Feel vibrations for interactions',
              value: userViewModel.getPreference('hapticFeedback', defaultValue: true),
              onChanged: (value) {
                userViewModel.updatePreferences({'hapticFeedback': value});
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountSection(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.fromLTRB(24, 0, 24, 24),
      child: ShadcnCardWidget(
        gradient: [
          theme.colorScheme.error.withValues(alpha: 0.08),
          theme.colorScheme.error.withValues(alpha: 0.04),
        ],
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_circle_outlined,
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                  size: 24,
                ),
                const SizedBox(width: 16),
                Text(
                  'Account',
                  style: theme.textTheme.titleLarge?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 24),
            _buildActionItem(
              theme: theme,
              icon: Icons.logout_outlined,
              title: 'Sign Out',
              subtitle: 'Sign out of your account',
              isDestructive: true,
              onTap: () {
                context.read<UserViewModel>().signOut();
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeOption({
    required ThemeData theme,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return Button.ghost(
      onPressed: onTap,
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildPreferenceToggle({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3),
          ),
          child: Icon(
            icon,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
            size: 16,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: theme.textTheme.titleSmall?.copyWith(
                  color: theme.colorScheme.onSurface,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                subtitle,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                  fontWeight: FontWeight.w300,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: value,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildActionItem({
    required ThemeData theme,
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    if (isDestructive) {
      return Button.destructive(
        onPressed: onTap,
        child: Row(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isDestructive
                  ? theme.colorScheme.error.withValues(alpha: 0.2)
                  : theme.colorScheme.primary.withValues(alpha: 0.2),
            ),
            child: Icon(
              icon,
              color: isDestructive
                  ? theme.colorScheme.error
                  : theme.colorScheme.primary,
              size: 16,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleSmall?.copyWith(
                    color: isDestructive
                        ? theme.colorScheme.error
                        : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
            size: 16,
          ),
        ],
        ),
      );
    } else {
      return Button.ghost(
        onPressed: onTap,
        child: Row(
          children: [
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.colorScheme.primary.withValues(alpha: 0.2),
              ),
              child: Icon(
                icon,
                color: theme.colorScheme.primary,
                size: 16,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: theme.colorScheme.onSurface.withValues(alpha: 0.4),
              size: 16,
            ),
          ],
        ),
      );
    }
  }

  void _showThemeSelector(BuildContext context, ThemeViewModel themeViewModel) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => _ShadcnThemeSelectorBottomSheet(themeViewModel: themeViewModel),
    );
  }

  String _getThemeModeText(ThemeMode mode) {
    switch (mode) {
      case ThemeMode.system:
        return 'System';
      case ThemeMode.light:
        return 'Light';
      case ThemeMode.dark:
        return 'Dark';
    }
  }
}

/// Shadcn-based theme selector bottom sheet
class _ShadcnThemeSelectorBottomSheet extends StatelessWidget {
  final ThemeViewModel themeViewModel;

  const _ShadcnThemeSelectorBottomSheet({required this.themeViewModel});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      margin: const EdgeInsets.all(16),
      child: ShadcnCardWidget(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Choose Theme',
              style: theme.textTheme.titleLarge?.copyWith(
                color: theme.colorScheme.onSurface,
                fontWeight: FontWeight.w400,
              ),
            ),
            const SizedBox(height: 24),
            _buildThemeModeOption(
              context,
              title: 'System',
              subtitle: 'Follow system settings',
              icon: Icons.brightness_auto_outlined,
              mode: ThemeMode.system,
            ),
            const SizedBox(height: 12),
            _buildThemeModeOption(
              context,
              title: 'Light',
              subtitle: 'Always use light theme',
              icon: Icons.light_mode_outlined,
              mode: ThemeMode.light,
            ),
            const SizedBox(height: 12),
            _buildThemeModeOption(
              context,
              title: 'Dark',
              subtitle: 'Always use dark theme',
              icon: Icons.dark_mode_outlined,
              mode: ThemeMode.dark,
            ),
            const SizedBox(height: 16),
          ],
        ),
      ),
    );
  }

  Widget _buildThemeModeOption(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required ThemeMode mode,
  }) {
    final theme = Theme.of(context);
    final isSelected = themeViewModel.themeMode == mode;

    if (isSelected) {
      return Button.primary(
        onPressed: () => themeViewModel.setThemeModeAndClose(mode),
        child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: isSelected
                  ? AppTheme.primaryGradient[0].withValues(alpha: 0.2)
                  : theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
            ),
            child: Icon(
              icon,
              color: isSelected
                  ? AppTheme.primaryGradient[0]
                  : theme.colorScheme.onSurface.withValues(alpha: 0.7),
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: theme.textTheme.titleMedium?.copyWith(
                    color: isSelected
                        ? AppTheme.primaryGradient[0]
                        : theme.colorScheme.onSurface,
                    fontWeight: FontWeight.w400,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  subtitle,
                  style: theme.textTheme.bodySmall?.copyWith(
                    color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                    fontWeight: FontWeight.w300,
                  ),
                ),
              ],
            ),
          ),
          if (isSelected)
            Icon(
              Icons.check_circle,
              color: AppTheme.primaryGradient[0],
              size: 20,
            ),
        ],
        ),
      );
    } else {
      return Button.ghost(
        onPressed: () => themeViewModel.setThemeModeAndClose(mode),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.5),
              ),
              child: Icon(
                icon,
                color: theme.colorScheme.onSurface.withValues(alpha: 0.7),
                size: 20,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: theme.colorScheme.onSurface,
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    subtitle,
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: theme.colorScheme.onSurface.withValues(alpha: 0.6),
                      fontWeight: FontWeight.w300,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      );
    }
  }
}