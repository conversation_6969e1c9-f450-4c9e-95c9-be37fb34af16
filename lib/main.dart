import 'package:flutter/material.dart' hide ThemeData, <PERSON>affold, ColorScheme, ThemeMode, Colors, TextField, Theme, Card;
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:shadcn_flutter/shadcn_flutter.dart';
import 'dart:developer' as developer;

import 'core/di/injection_container.dart';
import 'core/services/navigation_service.dart';
import 'presentation/viewmodels/theme_view_model.dart';
import 'presentation/viewmodels/user_view_model.dart';
import 'presentation/viewmodels/deck_view_model.dart';
import 'presentation/views/main_navigation_view_shadcn.dart';
import 'presentation/views/auth/login_view_shadcn.dart';

void main() async {
  try {
    WidgetsFlutterBinding.ensureInitialized();

    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);

    // Initialize MVVM dependencies
    await initializeDependencies();

    runApp(const FlashCardsApp());
  } catch (e, stackTrace) {
    developer.log(
      'Error during app initialization',
      name: 'main',
      error: e,
      stackTrace: stackTrace,
    );

    // Run a fallback app
    runApp(ErrorApp(error: e.toString()));
  }
}

class ErrorApp extends StatelessWidget {
  final String error;
  
  const ErrorApp({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Error App',
      home: Material(
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                const Text(
                  'App Initialization Error:',
                  style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                Text(
                  error,
                  style: const TextStyle(color: Color(0xFFD32F2F)),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class FlashCardsApp extends StatelessWidget {
  const FlashCardsApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // MVVM ViewModels
        ChangeNotifierProvider(
          create: (_) => getIt<ThemeViewModel>()..loadTheme(),
        ),
        ChangeNotifierProvider(
          create: (_) => getIt<UserViewModel>()..loadUserState(),
        ),
        ChangeNotifierProvider(
          create: (_) => getIt<DeckViewModel>()..loadDecks(),
        ),
      ],
      child: Consumer3<ThemeViewModel, UserViewModel, DeckViewModel>(
        builder: (context, themeViewModel, userViewModel, deckViewModel, _) {
          return DynamicColorBuilder(
            builder: (lightDynamic, darkDynamic) {
              return ShadcnApp(
                title: 'FlashCards Pro',
                debugShowCheckedModeBanner: false,
                navigatorKey: getIt<NavigationService>().navigatorKey,

                // Use shadcn theme configuration
                theme: _buildShadcnTheme(themeViewModel.themeMode),

                home: _buildHome(userViewModel),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildHome(UserViewModel userViewModel) {
    // Show loading while checking authentication state
    if (userViewModel.isLoading) {
      return const Scaffold(
        child: Center(
          child: Progress(),
        ),
      );
    }

    // Navigate based on authentication state
    if (userViewModel.isAuthenticated) {
      return const MainNavigationViewShadcn();
    } else {
      return const WelcomePage();
    }
  }

  ThemeData _buildShadcnTheme(dynamic themeMode) {
    // Use shadcn's built-in color schemes
    return ThemeData(
      colorScheme: ColorSchemes.lightZinc(),
      radius: 0.7, // Rounded corners
    );
  }
}

class WelcomePage extends StatelessWidget {
  const WelcomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF9333EA),
              const Color(0xFF7C3AED),
              const Color(0xFF6366F1),
            ],
          ),
        ),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(32.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // App Icon/Logo
                Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    color: const Color(0x33FFFFFF), // white with 0.2 opacity
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: const Icon(
                    Icons.school_rounded,
                    size: 60,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                const SizedBox(height: 40),

                // App Title
                const Text(
                  'FlashCards Pro',
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: Color(0xFFFFFFFF),
                  ),
                ),
                const SizedBox(height: 16),

                // Subtitle
                Text(
                  'Master any subject with intelligent flashcards',
                  style: TextStyle(
                    fontSize: 18,
                    color: Color(0xE6FFFFFF), // white with 0.9 opacity
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 60),

                // Get Started Button
                SizedBox(
                  width: double.infinity,
                  child: PrimaryButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacement(
                        MaterialPageRoute(
                          builder: (context) => const LoginViewShadcn(),
                        ),
                      );
                    },
                    child: const Padding(
                      padding: EdgeInsets.symmetric(vertical: 16.0),
                      child: Text(
                        'Get Started',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 20),

                // Demo Account Info
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color(0x1AFFFFFF), // white with 0.1 opacity
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: const Color(0x33FFFFFF), // white with 0.2 opacity
                    ),
                  ),
                  child: Column(
                    children: [
                      const Text(
                        'Demo Account',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Color(0xE6FFFFFF), // white with 0.9 opacity
                        ),
                      ),
                      const SizedBox(height: 8),
                      const Text(
                        'Email: <EMAIL>\nPassword: demo123',
                        style: TextStyle(
                          fontSize: 14,
                          color: Color(0xCCFFFFFF), // white with 0.8 opacity
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}